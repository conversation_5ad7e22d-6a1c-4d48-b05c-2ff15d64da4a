package hero.api.user.service

import hero.baseutils.minus
import hero.baseutils.minusDays
import hero.core.data.PageRequest
import hero.model.ImageAsset
import hero.model.SupportCounts
import hero.model.UserStatus.DELETED
import hero.test.IntegrationTest
import hero.test.TestRepositories
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds

class FeaturedCreatorsQueryServiceIT : IntegrationTest() {
    @Nested
    inner class GetCreatorsSortedBySubs {
        @Test
        fun `should return creators sorted by their supporters`() {
            val underTest = FeaturedCreatorsQueryService(lazyTestContext, TestRepositories.userRepository)

            val agata = testHelper.createUser("agataaornella", counts = SupportCounts(supporters = 6834))
            val cestmir = testHelper.createUser("cestmir", counts = SupportCounts(supporters = 10483))
            testHelper.createUser("deleted-user", counts = SupportCounts(supporters = 10483), status = DELETED)
            val zajic = testHelper.createUser("zajic", counts = SupportCounts(supporters = 7622))
            val meldikovic = testHelper.createUser("meldikovic", counts = SupportCounts(supporters = 3198))
            val yestv = testHelper.createUser("yestv", counts = SupportCounts(supporters = 5042))
            val bombyktyci = testHelper.createUser("bombyktyci", counts = SupportCounts(supporters = 3623))

            // should be ignored
            val infohero = testHelper.createUser("infoheroherokamniiih", counts = SupportCounts(supporters = 9232))

            val result = underTest.execute(
                GetCreatorsSortedBySubs(PageRequest(pageSize = 5), listOf("infoheroherokamniiih")),
            )

            assertThat(result.content).hasSize(5)
            assertThat(result.content)
                .containsExactly(cestmir, zajic, agata, yestv, bombyktyci)
                .doesNotContain(meldikovic, infohero)
        }
    }

    @Nested
    inner class GetCreatorsSortedByRecentPosts {
        @Test
        fun `should return creators that have 50 mores subs and sorted by recent posts`() {
            val underTest = FeaturedCreatorsQueryService(lazyTestContext, TestRepositories.userRepository)

            val now = Instant.ofEpochSecond(1742048547)

            val zajic = testHelper.createUser(
                "zajic",
                counts = SupportCounts(supporters = 7622),
                lastPostAt = now - 5.seconds,
            )

            val agata = testHelper.createUser(
                "agataaornella",
                counts = SupportCounts(supporters = 6834),
                lastPostAt = now - 15.seconds,
            )

            val cestmir = testHelper.createUser(
                "cestmir",
                counts = SupportCounts(supporters = 10483),
                lastPostAt = now - 10.seconds,
            )
            testHelper.createUser(
                "deleted-user",
                counts = SupportCounts(supporters = 10483),
                lastPostAt = now - 10.seconds,
                status = DELETED,
            )
            // should be ignored
            val infohero = testHelper.createUser(
                "infoheroherokamniiih",
                counts = SupportCounts(supporters = 9232),
                lastPostAt = now - 1.seconds,
            )

            // will not be returned since he does not have enough supporters
            val notEnoughSubs = testHelper.createUser(
                "noname",
                counts = SupportCounts(supporters = 30),
                lastPostAt = now - 10.seconds,
            )

            // will not be returned since he didn't post yet
            val noRecentPost = testHelper.createUser(
                "noname",
                counts = SupportCounts(supporters = 60),
                lastPostAt = null,
            )

            val result = underTest.execute(
                GetCreatorsSortedByRecentPosts(
                    PageRequest(pageSize = 5),
                    listOf("infoheroherokamniiih"),
                ),
            )

            assertThat(result.content).hasSize(3)
            assertThat(result.content)
                .containsExactly(zajic, cestmir, agata)
                .doesNotContain(notEnoughSubs, noRecentPost, infohero)
        }
    }

    @Nested
    inner class GetCreatorsSortedByMostNewSubs {
        @Test
        fun `should return creators sorted by number of new active subscribers since given date`() {
            val underTest = FeaturedCreatorsQueryService(lazyTestContext, TestRepositories.userRepository)
            val now = Instant.now()

            val zajic = testHelper.createUser("zajic")
            val agata = testHelper.createUser("agataaornella")
            val cestmir = testHelper.createUser("cestmir")

            // should be ignored
            val infohero = testHelper.createUser("infoheroherokamniiih")
            testHelper.createSubscription("info-fan1", "infoheroherokamniiih")

            testHelper.createSubscription("zajic-fan1", "zajic")
            testHelper.createSubscription("zajic-fan2", "zajic")
            testHelper.createSubscription("zajic-fan3", "zajic")

            testHelper.createSubscription("agata-fan1", "agataaornella")
            testHelper.createSubscription("agata-fan2-old-sub", "agataaornella", createdAt = now - 10.days)
            testHelper.createSubscription("agata-fan3-old-sub", "agataaornella", createdAt = now - 10.days)

            testHelper.createSubscription("cestmir-fan1", "cestmir")
            testHelper.createSubscription("cestmir-fan2", "cestmir")

            val result = underTest.execute(
                GetCreatorsSortedByMostNewSubs(PageRequest(pageSize = 5), now - 1.days, listOf("infoheroherokamniiih")),
            )

            assertThat(result.content).hasSize(3)
            assertThat(result.content).containsExactly(zajic, cestmir, agata).doesNotContain(infohero)
        }
    }

    @Nested
    inner class GetCreatorsSortedByVerifiedAt {
        @Test
        fun `should return newly verified creators that have 10 mores subs`() {
            val underTest = FeaturedCreatorsQueryService(lazyTestContext, TestRepositories.userRepository)

            val now = Instant.ofEpochSecond(1742048547)
            val profileImage = ImageAsset("https://expected.com", width = 50, 100, null)

            val zajic = testHelper.createUser(
                "zajic",
                counts = SupportCounts(supporters = 7622),
                verifiedAt = now - 5.seconds,
                image = profileImage,
            )

            val agata = testHelper.createUser(
                "agataaornella",
                counts = SupportCounts(supporters = 6834),
                verifiedAt = now - 15.seconds,
                image = profileImage,
            )

            val cestmir = testHelper.createUser(
                "cestmir",
                counts = SupportCounts(supporters = 10483),
                verifiedAt = now - 10.seconds,
                image = profileImage,
            )

            testHelper.createUser(
                "deleted-user",
                counts = SupportCounts(supporters = 10483),
                verifiedAt = now - 10.seconds,
                status = DELETED,
                image = profileImage,
            )

            // will not be returned since he does not have enough supporters
            val notEnoughSubs = testHelper.createUser(
                "noname",
                counts = SupportCounts(supporters = 0),
                verifiedAt = now - 10.seconds,
                image = profileImage,
            )

            // will not be returned since he was not verified yet
            val notVerifiedYet = testHelper.createUser(
                "noname",
                counts = SupportCounts(supporters = 60),
                verifiedAt = null,
                image = profileImage,
            )

            val noProfilePicture = testHelper.createUser(
                "noname",
                counts = SupportCounts(supporters = 60),
                verifiedAt = now - 1.seconds,
                image = null,
            )

            val oldAccount = testHelper.createUser(
                "noname",
                createdAt = Instant.now().minusDays(60),
                counts = SupportCounts(supporters = 60),
                verifiedAt = now - 1.seconds,
                image = profileImage,
            )

            // should be ignored
            val infohero = testHelper.createUser(
                "infoheroherokamniiih",
                counts = SupportCounts(supporters = 60),
                verifiedAt = now - 1.seconds,
                image = profileImage,
            )

            val result = underTest.execute(
                GetCreatorsSortedByVerifiedAt(
                    PageRequest(pageSize = 5),
                    listOf("infoheroherokamniiih"),
                ),
            )

            assertThat(result.content).hasSize(3)
            assertThat(result.content)
                .containsExactly(zajic, cestmir, agata)
                .doesNotContain(notEnoughSubs, notVerifiedYet, noProfilePicture, oldAccount, infohero)
        }
    }
}
