package hero.api.user.repository

import io.mockk.clearAllMocks
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

internal class ImagesRepositoryTest {
    private val imageRepository: ImageRepository = ImageRepository(false)

    @AfterEach
    fun clear() {
        clearAllMocks()
        unmockkAll()
    }

    @Test
    @Disabled("https://linear.app/herohero/issue/HH-4729/reenable-dimensions-test")
    fun dimensions() {
        val imageSize = imageRepository.dimensions("https://picsum.photos/id/0/600/400")
        assertEquals(600, imageSize.width)
        assertEquals(400, imageSize.height)
        assertTrue(10000 < imageSize.length)
    }
}
