package hero.api.community.controller.dto

import hero.api.user.controller.dto.exampleUserResponse
import hero.model.ImageAsset
import java.time.Instant
import java.util.UUID

val exampleCommunityResponse = CommunityResponse(
    id = UUID.randomUUID(),
    name = "name",
    description = "description",
    slug = "slug",
    ownerId = "owner-id",
    membersCount = 1,
    image = ImageAsset(
        id = "https://heroheroco-assets-devel.storage.googleapis.com/images/user/herotesterznwnkkwc/1618164526274.png",
        width = 0,
        height = 0,
        fileName = null,
        fileSize = null,
        hidden = false,
    ),
    createdAt = Instant.now(),
    isVerified = false,
    owner = exampleUserResponse,
)
