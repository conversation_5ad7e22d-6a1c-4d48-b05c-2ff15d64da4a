package hero.api.user.service

import hero.api.user.controller.UsersJsonApiController
import hero.api.user.controller.UsersJsonApiController.ValidationErrorType.ILLEGAL_STRING
import hero.api.user.controller.UsersJsonApiController.ValidationErrorType.LOWERCASE_ALPHANUMERIC
import hero.api.user.controller.UsersJsonApiController.ValidationErrorType.MAX_LENGTH_EXCEEDED
import hero.api.user.controller.UsersJsonApiController.ValidationErrorType.MIN_LENGTH_TWO
import hero.api.user.controller.UsersJsonApiController.ValidationErrorType.NAME_TAKEN
import hero.api.user.controller.UsersJsonApiController.ValidationErrorType.PATH_CHANGE_TOO_OFTEN
import hero.api.user.repository.pathUpdateableAfter
import hero.baseutils.removeNonAscii
import hero.exceptions.http.BadRequestException
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.model.ImageAsset
import hero.model.Path
import hero.model.User
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.repository.user.UserRepository
import java.time.Instant

class UserCommandService(
    private val usersCollection: TypedCollectionReference<User>,
    private val userRepository: UserRepository,
    private val pathsCollection: TypedCollectionReference<Path>,
    private val pubSub: PubSub,
) {
    fun execute(command: UpdateUserDetails): User {
        val user = userRepository.getById(command.userId)
        val (newPath, newPathChangedAt) = validatePath(command.path, user).getOrThrow()
        val newName = validateName(command.name, user).getOrThrow()
        val newBio = validateBio(command.bio, user).getOrThrow()
        val image = validateImage(command.profileImage, user).getOrThrow()
        val updatedCreator = user.creator.copy(
            emailPublic = command.emailPublic,
            emailInvoice = command.emailInvoice,
        )
        val updatedUser = user.copy(
            path = newPath,
            pathChanged = newPathChangedAt,
            name = newName,
            bio = newBio,
            image = image,
            isOfAge = command.isOfAge ?: user.isOfAge,
            creator = updatedCreator,
        )

        return if (updatedUser == user) {
            user
        } else {
            if (pathsCollection[newPath].fetch() == null) {
                pathsCollection[newPath].set(Path(id = newPath, userId = command.userId, created = Instant.now()))
            }
            usersCollection[command.userId].set(updatedUser)
            userRepository.save(updatedUser)
            pubSub.publish(UserStateChanged(UserStateChange.PATCHED, user))

            updatedUser
        }
    }

    private fun validateImage(
        profileImage: UpdateUserProfileImage?,
        user: User,
    ): Result<ImageAsset?> {
        if (profileImage?.url == user.image?.id) {
            return Result.success(user.image)
        }

        return profileImage
            ?.let {
                Result.success(
                    ImageAsset.of(id = it.url, width = it.width, height = it.height, fileName = null, fileSize = null),
                )
            }
            ?: Result.success(null)
    }

    private fun validateBio(
        newBio: String,
        user: User,
    ): Result<String> {
        if (user.bio == newBio) {
            return Result.success(newBio)
        }
        val badRequest = badRequestForProperty(property = "bio", value = newBio)

        if (newBio.length > 1500) {
            return Result.failure(badRequest(MAX_LENGTH_EXCEEDED))
        }

        return Result.success(newBio.trim())
    }

    private fun validateName(
        newName: String,
        user: User,
    ): Result<String> {
        if (user.name == newName) {
            return Result.success(newName)
        }
        val badRequest = badRequestForProperty(property = "name", value = newName)

        // TODO inconsistency with UserJsonApiController, where `< 2` is required
        if (newName.removeNonAscii().length < 3) {
            return Result.failure(badRequest(MIN_LENGTH_TWO))
        }

        if ("herohero" in newName.lowercase() && user.email != "<EMAIL>") {
            return Result.failure(badRequest(ILLEGAL_STRING))
        }

        return Result.success(newName)
    }

    private fun validatePath(
        newPath: String,
        user: User,
    ): Result<Pair<String, Instant>> {
        if (user.path == newPath) {
            return Result.success(newPath to user.pathChanged)
        }
        val badRequest = badRequestForProperty(property = "path", value = newPath)

        if (newPath.length < 3) {
            return Result.failure(badRequest(MIN_LENGTH_TWO))
        }

        if (!newPath.matches("[a-z0-9]+".toRegex())) {
            return Result.failure(badRequest(LOWERCASE_ALPHANUMERIC))
        }

        if (newPath in UsersJsonApiController.excludedPaths) {
            return Result.failure(badRequest(ILLEGAL_STRING))
        }

        val path = pathsCollection[newPath].fetch()
        if (path != null && path.userId != user.id) {
            return Result.failure(badRequest(NAME_TAKEN))
        }

        if (user.pathChanged.pathUpdateableAfter() > Instant.now()) {
            return Result.failure(badRequest(PATH_CHANGE_TOO_OFTEN))
        }

        return Result.success(newPath to Instant.now())
    }

    private fun badRequestForProperty(
        property: String,
        value: String,
    ) = { error: UsersJsonApiController.ValidationErrorType ->
        val validationException = ValidationExceptionError(property, value, error)
        BadRequestException(body = listOf(validationException), message = validationException.message())
    }

    private data class ValidationExceptionError(
        val property: String,
        val value: String,
        val error: UsersJsonApiController.ValidationErrorType,
    )

    private fun ValidationExceptionError.message(): String =
        "Field '$property' of value '$value' is invalid: ${error.name.lowercase()}"
}

data class UpdateUserDetails(
    val userId: String,
    val path: String,
    val bio: String,
    val name: String,
    val isOfAge: Boolean?,
    val profileImage: UpdateUserProfileImage?,
    val emailPublic: String?,
    val emailInvoice: String?,
)

data class UpdateUserProfileImage(val url: String, val height: Int, val width: Int)
