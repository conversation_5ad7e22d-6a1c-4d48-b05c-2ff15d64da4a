package hero.api.post.controller.dto

import hero.gjirafa.dto.exampleGjirafaAsset
import hero.gjirafa.dto.exampleGjirafaLiveAsset
import hero.model.CategoryDtoRelationship
import hero.model.Chapter
import hero.model.DocumentAsset
import hero.model.DocumentType
import hero.model.ImageAssetDto
import hero.model.MessageThreadDtoV2Relationship
import hero.model.PollDto
import hero.model.PollOptionDto
import hero.model.PostAssetDto
import hero.model.PostCounts
import hero.model.PostDto
import hero.model.PostDtoAttributes
import hero.model.PostDtoRelationship
import hero.model.PostDtoRelationships
import hero.model.UserDtoRelationship
import hero.model.YouTubeAsset
import hero.model.topics.PostState
import java.time.Instant

val postDtoExample = PostDto(
    id = "null",
    attributes = PostDtoAttributes(
        fullAsset = true,
        publishedAt = Instant.now(),
        pinnedAt = Instant.now(),
        state = PostState.PUBLISHED,
        text = "Text of the post.",
        textHtml = "<p>Text of the post.</p>",
        textDelta = "{Text of the post.}",
        excludeFromRss = false,
        poll = PollDto(
            id = "poll-id",
            options = listOf(
                PollOptionDto("option-1", "Option 1"),
                PollOptionDto("option-2", "Option 2"),
            ),
            deadline = Instant.now(),
        ),
        assets = listOf(
            PostAssetDto(
                image = ImageAssetDto("https://uploaded/image/url", 640, 480, "file.jpg", 100L),
                youTube = YouTubeAsset("5laRP4DP1Sc", 640, 480, "https://thumbnail.jpg"),
                gjirafa = exampleGjirafaAsset,
                gjirafaLive = exampleGjirafaLiveAsset,
                document = DocumentAsset("https://uploaded/image/url", DocumentType.PDF, "super.pdf", 100L),
                thumbnail = "https://uploaded/image/url",
                bunnyAsset = "https://bunny-asset",
                audioAsset = "https://audio-asset",
                thumbnailImage = ImageAssetDto("https://uploaded/image/url", 640, 480, "file.jpg", 100L),
            ),
        ),
        counts = PostCounts(
            comments = 117,
        ),
        price = 100,
        assetsCount = 2,
        chapters = listOf(
            Chapter("first", 13),
            Chapter("second", 200),
        ),
    ),
    relationships = PostDtoRelationships(
        parent = PostDtoRelationship("post-parent-id"),
        sibling = PostDtoRelationship("comment-sibling-id"),
        user = UserDtoRelationship("pet-lib-kdnbf"),
        messageThread = MessageThreadDtoV2Relationship("message-thread-id"),
        paymentsByUsers = listOf(UserDtoRelationship("a"), UserDtoRelationship("b"), UserDtoRelationship("c")),
        categories = listOf(CategoryDtoRelationship("concerts"), CategoryDtoRelationship("theatres")),
    ),
)
