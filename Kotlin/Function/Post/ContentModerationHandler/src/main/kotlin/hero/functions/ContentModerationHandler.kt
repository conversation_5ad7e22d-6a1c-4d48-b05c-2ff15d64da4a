package hero.functions

import com.google.cloud.vision.v1.AnnotateImageRequest
import com.google.cloud.vision.v1.Feature
import com.google.cloud.vision.v1.Image
import com.google.cloud.vision.v1.ImageAnnotatorClient
import com.google.cloud.vision.v1.ImageSource
import com.google.cloud.vision.v1.Likelihood
import com.google.cloud.vision.v1.SafeSearchAnnotation
import hero.baseutils.SystemEnv
import hero.baseutils.envPrefix
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.model.AssetAnnotation
import hero.model.AssetAnnotationResult
import hero.model.Post
import hero.model.PostAsset
import hero.model.SlackAccessory
import hero.model.SlackBlock
import hero.model.SlackBlockText
import hero.model.SlackMessage
import hero.model.topics.PostStateChange
import hero.model.topics.PostStateChanged

@Suppress("Unused")
class ContentModerationHandler(
    private val isProduction: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, isProduction),
    private val postsCollection: TypedCollectionReference<Post> = firestore.typedCollectionOf(Post),
    private val hostname: String = SystemEnv.hostname,
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
) : PubSubSubscriber<PostStateChanged>() {
    override fun consume(payload: PostStateChanged) {
        if (payload.stateChange == PostStateChange.DELETED) return

        val post = payload.post
        val requests = post
            .assets
            .mapNotNull { it.image?.id }
            .map {
                it
                    .toImage()
                    .toSafeSearchDetectionRequest()
            }
        if (requests.isEmpty()) return

        // After completing all of your requests, call the "close" method
        // on the client to safely clean up any remaining background resources.
        val response = ImageAnnotatorClient.create().use {
            requests.chunked(10).flatMap { chunkedRequests ->
                it.batchAnnotateImages(chunkedRequests).responsesList
            }
        }
        log.info("Post ${post.id} is weighted as: $response", mapOf("userId" to post.userId))

        post.assets
            .filter { it.image?.id != null }
            .forEachIndexed { index, postAsset ->
                if (response.size > index) {
                    val annotationsResponse = response[index].safeSearchAnnotation
                    postAsset.assetAnnotations = annotationsResponse.toAssetAnnotation()
                }
            }

        postsCollection[post.id].field(Post::assets).update(post.assets)

        val unsafeAssets = post.assets.filter { it.assetAnnotations?.shouldBeModerated() == true }
        if (unsafeAssets.isNotEmpty()) {
            pubSub.publish(
                SlackMessage(
                    channel = "alerts_content_${isProduction.envPrefix}",
                    blocks = listOf(headerBlock(post, payload.stateChange)) + unsafeAssets.mapNotNull { it.toBlock() },
                ),
            )
        }
    }

    private fun headerBlock(
        post: Post,
        stateChange: PostStateChange,
    ): SlackBlock {
        val header = """
            *<$hostname/${post.userId}/|${post.userId}>* changed a *<$hostname/${post.userId}/post/${post.id}|post>* to `$stateChange` which was flagged as unsafe.
            Flagged assets:
            
        """.trimIndent()
        return SlackBlock(SlackBlockText(header))
    }
}

private fun SafeSearchAnnotation.toAssetAnnotation(): AssetAnnotation =
    AssetAnnotation(
        adult = AssetAnnotationResult(adult.number, adult.name),
        spoof = AssetAnnotationResult(spoof.number, spoof.name),
        medical = AssetAnnotationResult(medical.number, medical.name),
        violence = AssetAnnotationResult(violence.number, violence.name),
        racy = AssetAnnotationResult(racy.number, racy.name),
    )

/**
 * adult - Represents the adult content likelihood for the image. Adult content may contain elements such as nudity,
 *   pornographic images or cartoons, or sexual activities.
 * spoof - The likelihood that a modification was made to the image's canonical version
 *   to make it appear funny or offensive.
 * medical - Likelihood that this is a medical image.
 * violence - Likelihood that this image contains violent content.
 * racy - Likelihood that the request image contains racy content.
 *   Racy content may include (but is not limited to) skimpy or sheer clothing, strategically covered nudity,
 *   lewd or provocative poses, or close-ups of sensitive body areas.
 *
 * For our use case we should moderate only adult, violence and racy content.
 */
private fun AssetAnnotation.shouldBeModerated(): Boolean =
    listOf(adult.value, violence.value, racy.value).any { it >= Likelihood.LIKELY.number }

private val prefixRegex = "^(https://|http://)?assets\\.herohero\\.co".toRegex()

// replace https://assets.herohero.co/devel/images/post/user-id/**********-182333347-0089.jpeg
// with gs://heroheroco-assets/devel/images/post/user-id/**********-182333347-0089.jpeg
private fun String.toImage(): Image {
    val request = ImageSource.newBuilder()
        .setGcsImageUri(
            this
                .replace(prefixRegex, "gs://heroheroco-assets")
                .substringBefore("?"),
        )
        .build()
    return Image.newBuilder().setSource(request).build()
}

private fun Image.toSafeSearchDetectionRequest(): AnnotateImageRequest =
    AnnotateImageRequest.newBuilder().setImage(this).addFeatures(safeSearchDetection).build()

private val safeSearchDetection = Feature.newBuilder().setType(Feature.Type.SAFE_SEARCH_DETECTION).build()

private fun PostAsset.toBlock(): SlackBlock? =
    when {
        image != null -> "${image?.id}?width=500"
        else -> null
    }?.let { assetLink ->
        val text = assetAnnotations
            ?.let { aa ->
                val adultValue = aa.adult.textValue
                val racyValue = aa.racy.textValue
                val violenceValue = aa.violence.textValue
                """ $assetLink -> `{"adult": "$adultValue", "racy": "$racyValue", "violence": "$violenceValue"}`"""
            }
            ?: assetLink
        SlackBlock(text = SlackBlockText(text), accessory = SlackAccessory(assetLink, "Flagged content"))
    }
