package hero.functions

import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.gcloud.FirestoreRef
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.firestore
import hero.gcloud.typedCollectionOf
import hero.gcloud.where
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.topics.DiscordConnectionChanged
import hero.model.topics.DiscordMemberChanged

@Suppress("Unused")
class DiscordConnectionHandler(
    private val production: Boolean = SystemEnv.isProduction,
    private val firestore: FirestoreRef = firestore(SystemEnv.cloudProject, production),
    private val subscribersCollection: TypedCollectionReference<Subscriber> = firestore.typedCollectionOf(Subscriber),
    private val pubSub: PubSub = PubSub(SystemEnv.environment, SystemEnv.cloudProject),
) : PubSubSubscriber<DiscordConnectionChanged>() {
    override fun consume(payload: DiscordConnectionChanged) {
        val statusChange = payload.statusChange
        val userId = payload.userId
        log.info("Discord connection for User $userId is now $statusChange.")
        // handle discord members for creators that connected user subscribe
        subscribersCollection
            .where(Subscriber::userId).isEqualTo(userId)
            .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
            .fetchAll()
            .also {
                log.info(
                    "Handling discord change $statusChange for User $userId and its ${it.size} subscriptions.",
                    mapOf("userId" to userId),
                )
            }
            .forEach {
                pubSub.publish(
                    DiscordMemberChanged(
                        userId = userId,
                        creatorId = it.creatorId,
                        statusChange = statusChange,
                    ),
                )
            }

        // add discord members for users that subscribe connected user
        subscribersCollection
            .where(Subscriber::creatorId).isEqualTo(userId)
            .and(Subscriber::status).isIn(SubscriberStatus.activeStatuses)
            .fetchAll()
            .also {
                log.info(
                    "Handling discord change $statusChange for Creator $userId and its ${it.size} subscribers.",
                    mapOf("userId" to userId, "creatorId" to userId),
                )
            }
            .forEach {
                pubSub.publish(
                    DiscordMemberChanged(
                        userId = it.userId,
                        creatorId = userId,
                        statusChange = statusChange,
                    ),
                )
            }
    }
}
