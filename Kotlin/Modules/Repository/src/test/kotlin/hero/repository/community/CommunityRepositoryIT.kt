package hero.repository.community

import hero.baseutils.plus
import hero.model.Community
import hero.model.ImageAsset
import hero.repository.RepositoryTest
import hero.repository.user
import hero.sql.jooq.Tables
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration.Companion.seconds

class CommunityRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should save community`() {
            val underTest = CommunityRepository(testContext)
            createUser(user("owner-id"))
            val now = Instant.ofEpochSecond(1756112286)

            underTest.save(
                Community(
                    id = UUID.fromString("fdde0ba2-48ad-464f-ba7e-dfbc5dd67ca6"),
                    name = "name",
                    description = "description",
                    slug = "slug",
                    ownerId = "owner-id",
                    membersCount = 1,
                    image = ImageAsset(
                        id = "image-id",
                        width = 100,
                        height = 50,
                        fileName = "file.jpg",
                    ),
                    createdAt = now,
                    updatedAt = now + 1.seconds,
                    deletedAt = now + 2.seconds,
                ),
            )

            with(testContext.selectFrom(Tables.COMMUNITY).fetchSingle()) {
                assertThat(id).isEqualTo(UUID.fromString("fdde0ba2-48ad-464f-ba7e-dfbc5dd67ca6"))
                assertThat(name).isEqualTo("name")
                assertThat(description).isEqualTo("description")
                assertThat(slug).isEqualTo("slug")
                assertThat(ownerId).isEqualTo("owner-id")
                assertThat(membersCount).isEqualTo(1)
                assertThat(profileImageUrl).isEqualTo("image-id")
                assertThat(profileImageHeight).isEqualTo(50)
                assertThat(profileImageWidth).isEqualTo(100)
                assertThat(createdAt).isEqualTo(now)
                assertThat(updatedAt).isEqualTo(now + 1.seconds)
                assertThat(deletedAt).isEqualTo(now + 2.seconds)
            }
        }
    }

    @Nested
    inner class GetById {
        @Test
        fun `should get community by id`() {
            val underTest = CommunityRepository(testContext)
            createUser(user("owner-id"))
            val now = Instant.ofEpochSecond(1756112286)

            val expectedCommunity = Community(
                id = UUID.fromString("fdde0ba2-48ad-464f-ba7e-dfbc5dd67ca6"),
                name = "name",
                description = "description",
                slug = "slug",
                ownerId = "owner-id",
                membersCount = 1,
                image = ImageAsset(
                    id = "image-id",
                    width = 100,
                    height = 50,
                    // community images don't store file names
                    fileName = null,
                ),
                createdAt = now,
                updatedAt = now + 1.seconds,
                deletedAt = now + 2.seconds,
            )
            underTest.save(expectedCommunity)

            val retrievedCommunity = underTest.getById(expectedCommunity.id)

            assertThat(retrievedCommunity).isEqualTo(expectedCommunity)
        }
    }

    @Nested
    inner class FindById {
        @Test
        fun `should find a community by id`() {
            val underTest = CommunityRepository(testContext)
            createUser(user("owner-id"))
            val now = Instant.ofEpochSecond(1756112286)

            val expectedCommunity = Community(
                id = UUID.fromString("fdde0ba2-48ad-464f-ba7e-dfbc5dd67ca6"),
                name = "name",
                description = "description",
                slug = "slug",
                ownerId = "owner-id",
                membersCount = 1,
                image = ImageAsset(
                    id = "image-id",
                    width = 100,
                    height = 50,
                    // community images don't store file names
                    fileName = null,
                ),
                createdAt = now,
                updatedAt = now + 1.seconds,
                deletedAt = now + 2.seconds,
            )
            underTest.save(expectedCommunity)

            val retrievedCommunity = underTest.findById(expectedCommunity.id)

            assertThat(retrievedCommunity).isEqualTo(expectedCommunity)
        }

        @Test
        fun `should return null if community does not exist`() {
            val underTest = CommunityRepository(testContext)

            assertThat(underTest.findById(UUID.randomUUID())).isNull()
        }
    }

    @Nested
    inner class FindByOwnerId {
        @Test
        fun `should find all communities for given owner`() {
            val underTest = CommunityRepository(testContext)
            createUser(user("owner-id-1"))
            val now = Instant.ofEpochSecond(1756112286)
            val expectedCommunity = Community(
                id = UUID.fromString("fdde0ba2-48ad-464f-ba7e-dfbc5dd67ca6"),
                name = "name",
                description = "description",
                slug = "slug-1",
                ownerId = "owner-id-1",
                membersCount = 1,
                image = null,
                createdAt = now,
                updatedAt = now + 1.seconds,
                deletedAt = now + 2.seconds,
            )
            underTest.save(expectedCommunity)

            // this is other user's community
            createUser(user("owner-id-2"))
            val otherCommunity = Community(
                id = UUID.fromString("ba482818-0702-4e16-8d2c-207b47652499"),
                name = "name",
                description = "description",
                slug = "slug-2",
                ownerId = "owner-id-2",
                membersCount = 1,
                image = null,
                createdAt = now,
                updatedAt = now + 1.seconds,
                deletedAt = now + 2.seconds,
            )
            underTest.save(otherCommunity)

            val ownerId1Communities = underTest.findByOwnerId("owner-id-1")
            assertThat(ownerId1Communities).containsExactly(expectedCommunity)
        }
    }
}
