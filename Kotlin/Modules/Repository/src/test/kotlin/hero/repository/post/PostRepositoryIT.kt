package hero.repository.post

import hero.baseutils.plusHours
import hero.exceptions.http.NotFoundException
import hero.jackson.fromJson
import hero.model.Chapter
import hero.model.DocumentAsset
import hero.model.DocumentType
import hero.model.GjirafaAsset
import hero.model.GjirafaLiveAsset
import hero.model.GjirafaStatus
import hero.model.ImageAsset
import hero.model.LiveVideoStatus
import hero.model.Poll
import hero.model.PollOption
import hero.model.PostAsset
import hero.model.PostAssetAnalysis
import hero.model.PostAssetAnalysisScore
import hero.model.PostCounts
import hero.model.YouTubeAsset
import hero.model.topics.PostState
import hero.repository.RepositoryTest
import hero.repository.community
import hero.repository.post
import hero.repository.user
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.POLL_OPTION
import hero.sql.jooq.Tables.POST
import hero.sql.jooq.Tables.POST_ASSET
import hero.test.logging.TestLogger
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.jooq.impl.DSL
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class PostRepositoryIT : RepositoryTest() {
    @Nested
    inner class Save {
        @Test
        fun `should export post object without assets to postgres`() {
            val underTest = PostRepository(testContext, TestLogger)

            createUser(user(id = "cestmir"))
            val community = createCommunity(
                community(
                    id = UUID.fromString("2420e678-78aa-4963-b37d-9c91ea56c9ee"),
                    ownerId = "cestmir",
                ),
            )

            underTest.save(
                post(
                    userId = "cestmir",
                    id = "post-id",
                    createdAt = Instant.ofEpochSecond(1738244100),
                    updatedAt = Instant.ofEpochSecond(1738244101),
                    publishedAt = Instant.ofEpochSecond(1738244102),
                    pinnedAt = Instant.ofEpochSecond(1738244103),
                    state = PostState.PUBLISHED,
                    communityId = community.id,
                    text = "text",
                    textHtml = "<h1>text</h1>",
                    textDelta = "text-delta",
                    counts = PostCounts(10, 5),
                    price = 25,
                    views = 30,
                    categories = listOf("sport", "dance"),
                    chapters = listOf(Chapter("chapter-title", 10)),
                    excludeFromRss = true,
                    isAgeRestricted = true,
                    isSponsored = true,
                ),
            )

            val postsResult = testContext
                .select(POST.asterisk())
                .from(POST)
                .fetch()

            assertThat(postsResult).hasSize(1)
            val exportedPost = postsResult[0]

            assertThat(exportedPost[POST.ID]).isEqualTo("post-id")
            assertThat(exportedPost[POST.USER_ID]).isEqualTo("cestmir")
            assertThat(exportedPost[POST.CREATED_AT]).isEqualTo(Instant.ofEpochSecond(1738244100))
            assertThat(exportedPost[POST.UPDATED_AT]).isEqualTo(Instant.ofEpochSecond(1738244101))
            assertThat(exportedPost[POST.PUBLISHED_AT]).isEqualTo(Instant.ofEpochSecond(1738244102))
            assertThat(exportedPost[POST.PINNED_AT]).isEqualTo(Instant.ofEpochSecond(1738244103))

            assertThat(exportedPost[POST.STATE]).isEqualTo("PUBLISHED")

            assertThat(exportedPost[POST.COMMUNITY_ID]).isEqualTo(community.id)

            assertThat(exportedPost[POST.TEXT]).isEqualTo("text")
            assertThat(exportedPost[POST.TEXT_HTML]).isEqualTo("<h1>text</h1>")
            assertThat(exportedPost[POST.TEXT_DELTA]).isEqualTo("text-delta")

            assertThat(exportedPost[POST.COMMENTS_COUNT]).isEqualTo(10)
            assertThat(exportedPost[POST.REPLIES_COUNT]).isEqualTo(5)

            assertThat(exportedPost[POST.PRICE]).isEqualTo(25)
            assertThat(exportedPost[POST.VIEWS]).isEqualTo(30)

            assertThat(exportedPost[POST.CATEGORIES]).isEqualTo(arrayOf("sport", "dance"))
            assertThat(exportedPost[POST.CHAPTERS].map { it.data().fromJson<Chapter>() })
                .isEqualTo(listOf(Chapter("chapter-title", 10)))

            assertThat(exportedPost[POST.EXCLUDE_FROM_RSS]).isTrue()
            assertThat(exportedPost[POST.IS_AGE_RESTRICTED]).isTrue()
            assertThat(exportedPost[POST.IS_SPONSORED]).isTrue()
        }

        @Test
        fun `should export multiple posts`() {
            val underTest = PostRepository(testContext, TestLogger)

            createUser(user(id = "cestmir"))

            underTest.saveAll(
                listOf(
                    post(
                        userId = "cestmir",
                        id = "post-id-1",
                        price = 30,
                        assets = listOf(
                            PostAsset(
                                image = ImageAsset("img-url", 10, 20, "file.jpg", 1000L),
                                analysis = PostAssetAnalysis(
                                    summary = "image summary",
                                    toxicityScore = PostAssetAnalysisScore(1, "not toxic"),
                                ),
                            ),
                        ),
                        createdAt = Instant.ofEpochSecond(1738241100),
                    ),
                    post(
                        userId = "cestmir",
                        id = "post-id-2",
                        price = 40,
                        assets = listOf(
                            PostAsset(
                                document = DocumentAsset("doc-url", DocumentType.GP5, "doc.pdf", 100L),
                            ),
                        ),
                        createdAt = Instant.ofEpochSecond(1738244100),
                    ),
                ),
            )

            val postsResult = testContext
                .select(POST.asterisk())
                .from(POST)
                .orderBy(POST.CREATED_AT.asc())
                .fetch()

            assertThat(postsResult).hasSize(2)
            val exportedPost1 = postsResult[0]

            assertThat(exportedPost1[POST.ID]).isEqualTo("post-id-1")
            assertThat(exportedPost1[POST.USER_ID]).isEqualTo("cestmir")
            assertThat(exportedPost1[POST.PRICE]).isEqualTo(30)

            val exportedPost2 = postsResult[1]

            assertThat(exportedPost2[POST.ID]).isEqualTo("post-id-2")
            assertThat(exportedPost2[POST.USER_ID]).isEqualTo("cestmir")
            assertThat(exportedPost2[POST.PRICE]).isEqualTo(40)

            val assetResults = testContext
                .select(POST_ASSET.asterisk())
                .from(POST_ASSET)
                .fetch()

            assertThat(assetResults).hasSize(2)
            val exportedAsset1 = assetResults[0]
            assertThat(exportedAsset1[POST_ASSET.POST_ID]).isEqualTo("post-id-1")
            assertThat(exportedAsset1[POST_ASSET.ASSET_TYPE]).isEqualTo("IMAGE")
            assertThat(exportedAsset1[POST_ASSET.ANALYSIS].data().fromJson<PostAssetAnalysis>())
                .isEqualTo(
                    PostAssetAnalysis(
                        summary = "image summary",
                        toxicityScore = PostAssetAnalysisScore(1, "not toxic"),
                    ),
                )
            assertThat(exportedAsset1[POST_ASSET.ASSET_TYPE]).isEqualTo("IMAGE")

            val exportedAsset2 = assetResults[1]
            assertThat(exportedAsset2[POST_ASSET.POST_ID]).isEqualTo("post-id-2")
            assertThat(exportedAsset2[POST_ASSET.ASSET_TYPE]).isEqualTo("DOCUMENT")
        }

        @Nested
        inner class SavePoll {
            @Test
            fun `should export poll object to postgres`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                // creating new poll
                val pollOption1 = PollOption(id = "poll-option-1", title = "option 1", voteCount = 10, index = 0)
                val pollOption2 = PollOption(id = "poll-option-2", title = "option 2", voteCount = 0, index = 1)
                val poll = Poll(
                    id = "poll-id",
                    deadline = Instant.ofEpochSecond(1738244100),
                    options = mapOf(),
                )
                val post = post(
                    userId = "cestmir",
                    id = "post-id",
                    poll = poll.copy(options = mapOf("poll-option-1" to pollOption1)),
                )
                underTest.save(post)

                with(testContext.selectFrom(Tables.POLL).fetchSingle()) {
                    assertThat(id).isEqualTo("poll-id")
                    assertThat(userId).isEqualTo("cestmir")
                    assertThat(deadline).isEqualTo(Instant.ofEpochSecond(1738244100))
                }

                val savedOption1 = testContext.selectFrom(POLL_OPTION).where(POLL_OPTION.ID.eq("poll-option-1"))
                with(savedOption1.fetchSingle()) {
                    assertThat(pollId).isEqualTo("poll-id")
                    assertThat(title).isEqualTo("option 1")
                    assertThat(voteCount).isEqualTo(10)
                }

                // adding option2 to the poll
                underTest.save(
                    post.copy(
                        poll = poll.copy(
                            options = mapOf(
                                "poll-option-1" to pollOption1,
                                "poll-option-2" to pollOption2,
                            ),
                        ),
                    ),
                )

                with(savedOption1.fetchSingle()) {
                    assertThat(pollId).isEqualTo("poll-id")
                    assertThat(title).isEqualTo("option 1")
                    assertThat(voteCount).isEqualTo(10)
                    assertThat(index).isEqualTo(0)
                }
                val savedOption2 = testContext.selectFrom(POLL_OPTION).where(POLL_OPTION.ID.eq("poll-option-2"))
                with(savedOption2.fetchSingle()) {
                    assertThat(pollId).isEqualTo("poll-id")
                    assertThat(title).isEqualTo("option 2")
                    assertThat(voteCount).isEqualTo(0)
                    assertThat(index).isEqualTo(1)
                }

                // removing option2
                underTest.save(post.copy(poll = poll.copy(options = mapOf("poll-option-1" to pollOption1))))

                with(savedOption1.fetchSingle()) {
                    assertThat(pollId).isEqualTo("poll-id")
                    assertThat(title).isEqualTo("option 1")
                    assertThat(voteCount).isEqualTo(10)
                    assertThat(index).isEqualTo(0)
                }
                assertThat(savedOption2.fetchOne()).isNull()
            }
        }

        @Nested
        inner class PostTypeResolution {
            @Test
            fun `should export post object with type POST`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(post(userId = "cestmir", id = "post-id"))

                val exportedPost = testContext
                    .select(POST.asterisk())
                    .from(POST)
                    .where(POST.ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPost[POST.TYPE]).isEqualTo("CONTENT_POST")
            }

            @Test
            fun `should export post object with type MESSAGE`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(post(userId = "cestmir", id = "post-id", messageThreadId = "message-thread-id"))

                val exportedPost = testContext
                    .select(POST.asterisk())
                    .from(POST)
                    .where(POST.ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPost[POST.TYPE]).isEqualTo("MESSAGE")
            }

            @Test
            fun `should export post object with type COMMENT`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(post(userId = "cestmir", id = "post-id", parentId = "parent-post-id"))

                val exportedPost = testContext
                    .select(POST.asterisk())
                    .from(POST)
                    .where(POST.ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPost[POST.TYPE]).isEqualTo("COMMENT")
            }
        }

        @Nested
        inner class PostAssets {
            @Test
            fun `should export image asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(PostAsset(image = ImageAsset("url", 10, 20, "file.jpg"))),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isNull()
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("IMAGE")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data().fromJson<ImageAsset>())
                    .isEqualTo(ImageAsset("url", 10, 20, "file.jpg"))
            }

            @Test
            fun `should export youtube asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(PostAsset(youTube = YouTubeAsset("url", 10, 20, "thumbnail"))),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isNull()
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("YOUTUBE")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data().fromJson<YouTubeAsset>()).isEqualTo(
                    YouTubeAsset("url", 10, 20, "thumbnail"),
                )
            }

            @Test
            fun `should export bunny asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(PostAsset(bunnyAsset = "bunny-asset")),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isNull()
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("BUNNY")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data()).isEqualTo("{\"url\": \"bunny-asset\"}")
            }

            @Test
            fun `should export document asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(
                            PostAsset(document = DocumentAsset("url", DocumentType.GP5, "doc.pdf", 1000L)),
                        ),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isNull()
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("DOCUMENT")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data().fromJson<DocumentAsset>()).isEqualTo(
                    DocumentAsset("url", DocumentType.GP5, "doc.pdf", 1000L),
                )
            }

            @Test
            fun `should export gjirafa asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(PostAsset(gjirafa = gjirafaAsset)),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isNull()
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("GJIRAFA")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data().fromJson<GjirafaAsset>()).isEqualTo(
                    gjirafaAsset,
                )
            }

            @Test
            fun `should export gjirafa asset with thumbnail`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(
                            PostAsset(
                                gjirafa = gjirafaAsset,
                                thumbnailImage = ImageAsset(id = "thumbnail-url", width = 10, height = 20, "file.jpg"),
                            ),
                        ),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isEqualTo("thumbnail-url")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL_HEIGHT]).isEqualTo(20)
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL_WIDTH]).isEqualTo(10)
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("GJIRAFA")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data().fromJson<GjirafaAsset>()).isEqualTo(
                    gjirafaAsset,
                )
            }

            @Test
            fun `should export gjirafa livestream asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                underTest.save(
                    post(
                        userId = "cestmir",
                        id = "post-id",
                        assets = listOf(
                            PostAsset(
                                gjirafaLive = GjirafaLiveAsset(
                                    "url",
                                    "playback",
                                    "channel",
                                    LiveVideoStatus.LIVE,
                                ),
                            ),
                        ),
                    ),
                )

                val exportedPostAsset = testContext
                    .select(POST_ASSET.asterisk())
                    .from(POST_ASSET)
                    .where(POST_ASSET.POST_ID.eq("post-id"))
                    .fetchSingle()

                assertThat(exportedPostAsset[POST_ASSET.POST_ID]).isEqualTo("post-id")
                assertThat(exportedPostAsset[POST_ASSET.THUMBNAIL]).isNull()
                assertThat(exportedPostAsset[POST_ASSET.ASSET_TYPE]).isEqualTo("GJIRAFA_LIVESTREAM")
                assertThat(exportedPostAsset[POST_ASSET.METADATA].data().fromJson<GjirafaLiveAsset>()).isEqualTo(
                    GjirafaLiveAsset(
                        "url",
                        "playback",
                        "channel",
                        LiveVideoStatus.LIVE,
                    ),
                )
            }
        }
    }

    @Nested
    inner class Find {
        @Test
        fun `should fetch saved posts without assets`() {
            val underTest = PostRepository(testContext, TestLogger)

            createUser(user(id = "cestmir"))
            val expectedPost = post(
                userId = "cestmir",
                id = "post-id",
                createdAt = Instant.ofEpochSecond(1738244100),
                updatedAt = Instant.ofEpochSecond(1738244101),
                publishedAt = Instant.ofEpochSecond(1738244102),
                pinnedAt = Instant.ofEpochSecond(1738244103),
                state = PostState.PUBLISHED,
                text = "text",
                textHtml = "<h1>text</h1>",
                textDelta = "text-delta",
                counts = PostCounts(10, 5),
                price = 25,
                categories = listOf("sport", "dance"),
                chapters = listOf(Chapter("chapter-title", 10)),
                excludeFromRss = true,
                isAgeRestricted = true,
                isSponsored = true,
            )

            underTest.save(expectedPost)

            val results = underTest.find { this }

            assertThat(results).containsExactly(expectedPost)
        }

        @Nested
        inner class PostAssetTypeMapping {
            @Test
            fun `should correctly map post with document asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                val expectedPost = post(
                    userId = "cestmir",
                    id = "post-id",
                    assets = listOf(
                        PostAsset(document = DocumentAsset("url", DocumentType.GP5, "doc.pdf", 1000L)),
                    ),
                )
                underTest.save(expectedPost)

                val results = underTest.find { this }

                assertThat(results).containsExactly(expectedPost)
            }

            @Test
            fun `should correctly map post with gjirafa asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                val expectedPost = post(
                    userId = "cestmir",
                    id = "post-id",
                    assets = listOf(PostAsset(gjirafa = gjirafaAsset)),
                )
                underTest.save(expectedPost)

                val results = underTest.find { this }

                assertThat(results).containsExactly(expectedPost)
            }

            @Test
            fun `should correctly map post with gjirafa live asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                val expectedPost = post(
                    userId = "cestmir",
                    id = "post-id",
                    assets = listOf(
                        PostAsset(
                            gjirafaLive = GjirafaLiveAsset(
                                id = "live-id",
                                playbackUrl = "playback",
                                channelPublicId = "channel",
                                liveStatus = LiveVideoStatus.LIVE,
                            ),
                        ),
                    ),
                )
                underTest.save(expectedPost)

                val results = underTest.find { this }

                assertThat(results).containsExactly(expectedPost)
            }

            @Test
            fun `should correctly map post with image asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                val expectedPost = post(
                    userId = "cestmir",
                    id = "post-id",
                    assets = listOf(PostAsset(image = ImageAsset("url", 10, 20, "file.jpg"))),
                )
                underTest.save(expectedPost)

                val results = underTest.find { this }

                assertThat(results).containsExactly(expectedPost)
            }

            @Test
            fun `should correctly map post with youtube asset`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                val expectedPost = post(
                    userId = "cestmir",
                    id = "post-id",
                    assets = listOf(PostAsset(youTube = YouTubeAsset("url", 10, 20, "thumbnail"))),
                )
                underTest.save(expectedPost)

                val results = underTest.find { this }

                assertThat(results).containsExactly(expectedPost)
            }

            @Test
            fun `should correctly map post with multiple assets`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))

                val expectedPost = post(
                    userId = "cestmir",
                    id = "post-id",
                    assets = listOf(
                        PostAsset(image = ImageAsset("url", 10, 20, "file.jpg")),
                        PostAsset(gjirafa = gjirafaAsset),
                    ),
                )
                underTest.save(expectedPost)

                val results = underTest.find { this }

                assertThat(results).containsExactly(expectedPost)
            }
        }

        @Nested
        inner class Conditions {
            @Test
            fun `should correctly apply where condition`() {
                val underTest = PostRepository(testContext, TestLogger)

                createUser(user(id = "cestmir"))
                val expectedPost1 = post(userId = "cestmir", id = "cestmir-post-id")
                underTest.save(expectedPost1)

                createUser(user(id = "ester"))
                val expectedPost2 = post(userId = "ester", id = "ester-post-id")
                underTest.save(expectedPost2)

                val results = underTest.find { this.where(POST.USER_ID.eq("ester")) }

                assertThat(results).doesNotContain(expectedPost1)
                assertThat(results).containsExactly(expectedPost2)
            }
        }
    }

    @Nested
    inner class FindSingle {
        @Test
        fun `should find a single post by a condition`() {
            val underTest = PostRepository(testContext, TestLogger)
            createUser(user("cestmir"))
            val expectedPost = createPost(
                post(userId = "cestmir", assets = listOf(PostAsset(gjirafa = gjirafaAsset.copy(id = "vjsnluej")))),
            )
            createPost(
                post(userId = "cestmir", assets = listOf(PostAsset(gjirafa = gjirafaAsset.copy(id = "vjskquke")))),
            )

            val result = underTest.findSingle {
                this
                    .join(POST_ASSET)
                    .on(POST_ASSET.POST_ID.eq(POST.ID))
                    .where(DSL.jsonbGetAttributeAsText(POST_ASSET.METADATA, "id").eq("vjsnluej"))
            }

            assertThat(expectedPost).isEqualTo(result)
        }
    }

    @Nested
    inner class GetById {
        @Test
        fun `should get post by id`() {
            val underTest = PostRepository(testContext, TestLogger)

            createUser(user("cestmir"))
            val expectedPost = post(
                userId = "cestmir",
                id = "cestmir-post",
                assets = listOf(
                    PostAsset(
                        image = ImageAsset("img-url", 10, 20, "file.jpg"),
                        analysis = PostAssetAnalysis(
                            summary = "image summary",
                            toxicityScore = PostAssetAnalysisScore(1, "not toxic"),
                        ),
                    ),
                ),
            )
            createPost(expectedPost)

            val post = underTest.getById("cestmir-post")

            assertThat(post).isEqualTo(expectedPost)
        }

        @Test
        fun `should throw if post with given id does not exist`() {
            val underTest = PostRepository(testContext, TestLogger)

            assertThatExceptionOfType(NotFoundException::class.java).isThrownBy {
                underTest.getById("non-existent-id")
            }
        }
    }

    @Nested
    inner class FindById {
        @Test
        fun `should find a post by id`() {
            val underTest = PostRepository(testContext, TestLogger)

            createUser(user("cestmir"))
            val expectedPost = post(userId = "cestmir", id = "cestmir-post")
            createPost(expectedPost)

            val post = underTest.findById("cestmir-post")

            assertThat(post).isEqualTo(expectedPost)
        }

        @Test
        fun `should throw if post with given id does not exist`() {
            val underTest = PostRepository(testContext, TestLogger)

            val post = underTest.findById("non-existent-id")

            assertThat(post).isNull()
        }
    }
}

private val gjirafaAsset = GjirafaAsset(
    id = "vjsnlpex",
    width = 1280,
    height = 720,
    thumbnailBlurhash = "LGSF;JIUofof00RjWBay4nofofj[",
    duration = 1974.138,
    hasVideo = true,
    hasAudio = true,
    progressTillCompleteness = 50,
    progressTillReadiness = 70,
    hidden = false,
    audioByteSize = 268358756,
    status = GjirafaStatus.COMPLETE,
    key = "mdygiendyisjkahakfdn",
    audioStaticUrl = "https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/mp3/320kbps.mp3",
    audioStreamUrl = "https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/hls/360p/index.m3u8",
    videoStreamUrl = "https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/hls/master_file.m3u8",
    chaptersVttUrl = "https://cdn.vpplayer.tech/agmipobm/pLZw5imXMLYIFUFQLCeCag/encode/vjsnlpex/hls/chapter.vtt",
    keyId = "E03AF87C-9F1C-E64C-9BCC-15EC6A3FA352",
    encodingRemainingTime = 600.23,
    encodingFinishTime = Instant.now().plusHours(1),
    createdAt = Instant.now(),
    projectId = "project-id",
    imageKey = "12345798",
    originalFileName = "original-file-name.mp4",
    originalFileSize = 1_000_000,
)
