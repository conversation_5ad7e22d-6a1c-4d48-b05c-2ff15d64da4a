import { MessageAssetResolvers, MessageResolvers, QueryResolvers } from '../generated/resolvers-types'
import { GraphQLError } from 'graphql/error'
import { unauthorizedError, validateUserData } from './utils'
import { featureFlags } from '../common/feature-flags'

export const queries: QueryResolvers = {
    community: async (_, { id }, { dataSources }) => {
        return await dataSources.communityAPI.getCommunity(id)
    },

    notifications: async (_, { first, before, after, filter }, { dataSources, user }) => {
        validateUserData(user)
        const { notifications, pagination } = await dataSources.notificationAPI.getNotifications(
            {
                first,
                before,
                after,
            },
            {
                categories: filter?.categories ?? undefined,
            }
        )
        return {
            nodes: notifications,
            pageInfo: pagination,
        }
    },

    notificationSettings: async (_, __, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }

        return dataSources.notificationSettingsAPI.getNotificationSettings()
    },

    subscribeRequests: async (_, { first, after }, { dataSources, user }) => {
        validateUserData(user)
        const { subscribeRequests, pagination } = await dataSources.subscribeRequestAPI.getSubscribeRequests({
            first,
            after,
        })
        return {
            nodes: subscribeRequests,
            pageInfo: pagination,
        }
    },

    messageThread: async (_, { messageThreadId }, { dataSources }) => {
        return await dataSources.messageThreadAPI.getMessageThread(messageThreadId)
    },

    messageThreads: async (_, { first, after }, { dataSources }) => {
        const { messageThreads, pagination } = await dataSources.messageThreadAPI.getMessageThreads({ first, after })
        return {
            nodes: messageThreads,
            pageInfo: pagination,
        }
    },

    messages: async (_, { messageThreadId, first, after, before }, { dataSources }) => {
        const { messages, pagination } = await dataSources.messageThreadAPI.getMessages(messageThreadId, {
            first,
            after,
            before,
        })
        return {
            nodes: messages,
            pageInfo: pagination,
        }
    },

    livestreams: async (_, __, { dataSources, user }) => {
        if (!user) {
            throw unauthorizedError()
        }
        const { posts, pagination } = await dataSources.postAPI.getLivestreams(user.id)
        return {
            nodes: posts,
            pageInfo: pagination,
        }
    },

    posts: async (_, { id, first, after, before, filter, sort, reverse }, { dataSources }) => {
        const { posts, pagination } = await dataSources.postAPI.getPosts(
            filter?.creatorId ?? id ?? undefined,
            {
                first,
                after,
                before,
            },
            sort ?? {},
            filter ?? {}
        )
        if (reverse) {
            posts.reverse()
        }
        return {
            nodes: posts,
            pageInfo: pagination,
        }
    },

    searchPosts: async (_, { filter }, { dataSources }) => {
        if (!filter?.query) {
            return {
                nodes: [],
                pageInfo: {
                    hasNextPage: false,
                },
            }
        }

        const { posts, pagination } = featureFlags.postgresPostSearch
            ? await dataSources.postAPI.getPosts(undefined, {}, {}, { term: filter.query })
            : await dataSources.postAPI.getPostsJsonAPI({ query: filter.query })

        return {
            nodes: posts,
            pageInfo: pagination,
        }
    },

    post: async (_, { id }, { dataSources }) => {
        return dataSources.postAPI.getPost(id)
    },

    comments: async (_, { id, first, before, after, sortDirection }, { dataSources }) => {
        const { comments, pagination } = await dataSources.postAPI.getComments(id, {
            first,
            after,
            before,
            sortDirection,
        })
        return {
            nodes: comments,
            pageInfo: pagination,
        }
    },

    comment: async (_, { id }, { dataSources }) => {
        return await dataSources.postAPI.getComment(id)
    },

    user: async (_, { id }, { dataSources }) => {
        return dataSources.userAPI.getUser(id)
    },

    adminUserDetails: async (_, { id }, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }
        return dataSources.userAPI.getUserDetailsAsAdmin(id)
    },

    viewer: async (_, __, { dataSources, user }) => {
        if (!user?.id) {
            throw new GraphQLError('Unauthorized', {
                extensions: {
                    code: 'UNAUTHORIZED',
                },
            })
        }
        return dataSources.userAPI.getUserDetails(user.id)
    },

    subscribers: async (_, { creatorId, first, after, orderBy }, { dataSources }) => {
        const { subscriptions, pagination } = await dataSources.subscriptionAPI.getSubscribers(creatorId, orderBy, {
            first,
            after,
        })

        return {
            nodes: subscriptions,
            pageInfo: pagination,
        }
    },

    subscriptions: async (_, { userId, first, after, orderBy, filter }, { dataSources }) => {
        const { subscriptions, pagination } = await dataSources.subscriptionAPI.getSubscriptions(
            userId,
            orderBy,
            {
                first,
                after,
            },
            filter ?? undefined
        )

        return {
            nodes: subscriptions,
            pageInfo: pagination,
        }
    },

    searchUsers: async (_, { query, first }, { dataSources }) => {
        const users = featureFlags.postgresUserSearch
            ? await dataSources.userAPI.searchUsers(query, { first })
            : await dataSources.userAPI.searchUsersDeprecated(query, { first })

        return {
            nodes: users,
            pageInfo: {
                hasNextPage: false,
            },
        }
    },

    featuredCreators: async (_, { locale, first }, { dataSources, user }) => {
        let resolvedLocale = locale
        if (!resolvedLocale) {
            if (!user) {
                resolvedLocale = 'en'
            } else {
                const currentUser = await dataSources.userAPI.getUserDetails(user.id)
                resolvedLocale = currentUser.language
            }
        }
        const users = await dataSources.userAPI.featuredUsers(resolvedLocale, { first })

        return {
            nodes: users,
            pageInfo: {
                hasNextPage: false,
            },
        }
    },

    featuredCreatorsRandomized: async (_, { featuredCategory }, { dataSources }) => {
        const users = await dataSources.userAPI.featuredCreatorsRandomized(featuredCategory)

        return {
            users,
        }
    },

    savedPosts: async (_, { first, after, filter }, { dataSources }) => {
        const { savedPosts, pagination } = await dataSources.libraryAPI.getSavedPosts(
            {
                first,
                after,
            },
            filter?.subscribedCreatorsOnly ?? undefined
        )

        return {
            nodes: savedPosts,
            pageInfo: pagination,
        }
    },

    gjirafaVideoQualities: async (_, { assetId }, { dataSources }) => {
        return dataSources.gjirafaAPI.getVideoQualities(assetId)
    },

    expectedIncome: async (_, __, { dataSources, user }) => {
        if (!user?.id) {
            throw unauthorizedError()
        }
        return dataSources.statisticsAPI.getExpectedIncome(user.id)
    },
}

export const messageResolvers: MessageResolvers = {
    sentBy: async ({ sentById }, _, { dataSources }) => {
        return dataSources.userAPI.getUser(sentById)
    },
}

export const messageAssetResolvers: MessageAssetResolvers = {
    __resolveType: ({ assetType }) => {
        switch (assetType) {
            case 'image':
                return 'PostImageAsset'
            case 'gjirafa':
                return 'PostGjirafaAsset'
            case 'document':
                return 'PostDocumentAsset'
            case 'empty':
                return 'MessageLockedAsset'
            default:
                throw Error(`Unknown asset type ${assetType}`)
        }
    },
}
